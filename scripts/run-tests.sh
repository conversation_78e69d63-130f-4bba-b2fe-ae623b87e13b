#!/bin/bash

# 运行测试脚本

set -e

echo "Running Flink Data Cleaning Project Tests..."

# 设置Java环境变量
export JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-11-openjdk}
export PATH=$JAVA_HOME/bin:$PATH

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 创建必要的目录
mkdir -p logs
mkdir -p target/test-reports

echo "Compiling project..."
mvn clean compile test-compile -q

echo "Running unit tests..."
mvn test -q

echo "Generating test report..."
mvn surefire-report:report -q

echo "Tests completed successfully!"
echo "Test reports are available in: target/site/surefire-report.html"

# 显示测试结果摘要
if [ -f "target/surefire-reports/TEST-*.xml" ]; then
    echo ""
    echo "Test Summary:"
    grep -h "testcase" target/surefire-reports/TEST-*.xml | wc -l | xargs echo "Total tests:"
    grep -h "failure\|error" target/surefire-reports/TEST-*.xml | wc -l | xargs echo "Failed tests:"
fi
