#!/bin/bash

# Flink数据清洗作业Kafka模式运行脚本

set -e

echo "Starting Flink Data Cleaning Job (Kafka Mode)..."

# 设置Java环境变量
export JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-11-openjdk}
export PATH=$JAVA_HOME/bin:$PATH

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 默认Kafka配置
KAFKA_BOOTSTRAP_SERVERS=${KAFKA_BOOTSTRAP_SERVERS:-"localhost:9092"}
KAFKA_INPUT_TOPIC=${KAFKA_INPUT_TOPIC:-"user-events"}
KAFKA_OUTPUT_TOPIC=${KAFKA_OUTPUT_TOPIC:-"cleaned-user-events"}
KAFKA_GROUP_ID=${KAFKA_GROUP_ID:-"data-cleaning-group"}

echo "Kafka Configuration:"
echo "  Bootstrap Servers: $KAFKA_BOOTSTRAP_SERVERS"
echo "  Input Topic: $KAFKA_INPUT_TOPIC"
echo "  Output Topic: $KAFKA_OUTPUT_TOPIC"
echo "  Group ID: $KAFKA_GROUP_ID"

# 创建必要的目录
mkdir -p logs

# 编译项目
echo "Building project..."
mvn clean compile -q

# 运行作业
echo "Running Flink Data Cleaning Job with Kafka..."
mvn exec:java -Dexec.mainClass="com.example.flink.DataCleaningJob" \
    -Dexec.args="--source-type kafka --sink-type kafka --kafka-bootstrap-servers $KAFKA_BOOTSTRAP_SERVERS --kafka-topic $KAFKA_INPUT_TOPIC --kafka-output-topic $KAFKA_OUTPUT_TOPIC --kafka-group-id $KAFKA_GROUP_ID" \
    -q

echo "Job completed successfully!"
