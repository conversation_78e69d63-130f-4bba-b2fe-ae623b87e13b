#!/bin/bash

# Flink数据清洗作业本地运行脚本

set -e

echo "Starting Flink Data Cleaning Job (Local Mode)..."

# 设置Java环境变量
export JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-11-openjdk}
export PATH=$JAVA_HOME/bin:$PATH

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 创建必要的目录
mkdir -p logs
mkdir -p output
mkdir -p input

# 检查输入文件是否存在
if [ ! -f "input/user_events.json" ]; then
    echo "Warning: Input file 'input/user_events.json' not found!"
    echo "Please ensure the input file exists before running the job."
    exit 1
fi

# 编译项目
echo "Building project..."
mvn clean compile -q

# 运行作业
echo "Running Flink Data Cleaning Job..."
mvn exec:java -Dexec.mainClass="com.example.flink.DataCleaningJob" \
    -Dexec.args="--source-type file --sink-type file --input-path input/user_events.json --output-path output/cleaned_events" \
    -q

echo "Job completed successfully!"
echo "Check the output directory: output/cleaned_events"
