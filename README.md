# Flink数据清洗项目

这是一个基于Apache Flink的实时数据清洗项目，用于处理用户行为事件数据，提供完整的数据验证、清洗、去重和标准化功能。

## 项目特性

### 核心功能
- **数据验证**: 验证输入数据的格式和完整性
- **数据清洗**: 标准化数据格式，处理异常值
- **数据去重**: 基于时间窗口的智能去重
- **数据脱敏**: IP地址脱敏，保护用户隐私
- **URL标准化**: 清理和标准化URL，移除跟踪参数
- **User Agent解析**: 提取浏览器、操作系统等信息
- **机器人检测**: 识别和标记机器人流量

### 技术栈
- **Apache Flink 1.18.0**: 流处理引擎
- **Java 11**: 编程语言
- **Maven**: 项目管理和构建工具
- **Jackson**: JSON序列化/反序列化
- **JUnit 5**: 单元测试框架
- **SLF4J + Log4j2**: 日志框架

## 项目结构

```
FinkDemo/
├── src/
│   ├── main/
│   │   ├── java/com/example/flink/
│   │   │   ├── DataCleaningJob.java              # 主作业类
│   │   │   ├── model/                            # 数据模型
│   │   │   │   ├── UserEvent.java                # 原始事件模型
│   │   │   │   └── CleanedUserEvent.java         # 清洗后事件模型
│   │   │   ├── function/                         # 处理函数
│   │   │   │   ├── DataCleaningFunction.java     # 数据清洗函数
│   │   │   │   ├── DataValidationFunction.java   # 数据验证函数
│   │   │   │   └── DeduplicationFunction.java    # 去重函数
│   │   │   ├── util/                             # 工具类
│   │   │   │   ├── DataValidationUtil.java       # 数据验证工具
│   │   │   │   ├── IpMaskingUtil.java            # IP脱敏工具
│   │   │   │   ├── UrlNormalizationUtil.java     # URL标准化工具
│   │   │   │   └── UserAgentParserUtil.java      # User Agent解析工具
│   │   │   └── serialization/                    # 序列化类
│   │   │       ├── UserEventDeserializationSchema.java
│   │   │       └── CleanedUserEventSerializationSchema.java
│   │   └── resources/
│   │       ├── application.properties            # 应用配置
│   │       └── log4j2.xml                       # 日志配置
│   └── test/                                    # 测试代码
├── input/                                       # 输入数据目录
│   └── user_events.json                        # 示例数据
├── scripts/                                    # 运行脚本
│   ├── run-local.sh                            # 本地运行脚本
│   ├── run-with-kafka.sh                       # Kafka模式运行脚本
│   └── run-tests.sh                            # 测试运行脚本
├── pom.xml                                     # Maven配置文件
└── README.md                                   # 项目说明文档
```

## 快速开始

### 环境要求
- Java 11或更高版本
- Maven 3.6或更高版本
- Apache Flink 1.18.0（可选，用于集群部署）

### 1. 克隆项目
```bash
git clone <repository-url>
cd FinkDemo
```

### 2. 编译项目
```bash
mvn clean compile
```

### 3. 运行测试
```bash
# 使用脚本运行测试
./scripts/run-tests.sh

# 或直接使用Maven
mvn test
```

### 4. 本地运行（文件模式）
```bash
# 使用脚本运行
./scripts/run-local.sh

# 或直接使用Maven
mvn exec:java -Dexec.mainClass="com.example.flink.DataCleaningJob" \
    -Dexec.args="--source-type file --sink-type file --input-path input/user_events.json --output-path output/cleaned_events"
```

### 5. Kafka模式运行
```bash
# 确保Kafka服务正在运行
# 使用脚本运行
./scripts/run-with-kafka.sh

# 或直接使用Maven
mvn exec:java -Dexec.mainClass="com.example.flink.DataCleaningJob" \
    -Dexec.args="--source-type kafka --sink-type kafka --kafka-bootstrap-servers localhost:9092 --kafka-topic user-events --kafka-output-topic cleaned-user-events"
```

## 配置说明

### 命令行参数
- `--source-type`: 数据源类型 (file/kafka)
- `--sink-type`: 数据输出类型 (file/kafka/both)
- `--input-path`: 输入文件路径（文件模式）
- `--output-path`: 输出文件路径（文件模式）
- `--kafka-bootstrap-servers`: Kafka服务器地址
- `--kafka-topic`: Kafka输入主题
- `--kafka-output-topic`: Kafka输出主题
- `--kafka-group-id`: Kafka消费者组ID

### 应用配置
主要配置在 `src/main/resources/application.properties` 中：

```properties
# Kafka配置
kafka.bootstrap.servers=localhost:9092
kafka.input.topic=user-events
kafka.output.topic=cleaned-user-events

# 数据清洗配置
cleaning.deduplication.window.seconds=300
cleaning.ip.masking.level=MEDIUM
cleaning.url.remove.tracking.params=true

# 性能配置
flink.parallelism=4
flink.checkpoint.interval=60000
```

## 数据格式

### 输入数据格式 (UserEvent)
```json
{
  "user_id": "user_001",
  "event_type": "page_view",
  "timestamp": "2024-01-15 10:30:00",
  "page_url": "https://example.com/home?utm_source=google",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "ip_address": "*************",
  "session_id": "sess_abc123def456",
  "device_type": "desktop",
  "browser": "Chrome",
  "location": "Beijing,China",
  "duration": 45
}
```

### 输出数据格式 (CleanedUserEvent)
```json
{
  "user_id": "user_001",
  "event_type": "page_view",
  "timestamp": "2024-01-15 10:30:00",
  "page_url": "https://example.com/home?utm_source=google",
  "normalized_url": "https://example.com/home",
  "ip_address": "*************",
  "masked_ip": "192.168.*.*",
  "session_id": "sess_abc123def456",
  "device_type": "desktop",
  "browser": "Chrome",
  "browser_version": "120.0.0.0",
  "location": "Beijing,China",
  "country": "Beijing",
  "city": "China",
  "duration": 45,
  "is_bot": false,
  "is_mobile": false,
  "processing_timestamp": "2024-01-15 10:30:01"
}
```

## 数据清洗规则

### 1. 数据验证
- 验证必填字段：user_id, event_type, timestamp, session_id
- 验证数据格式：IP地址、URL、时间戳等
- 验证数据范围：持续时间、时间戳合理性等

### 2. 数据清洗
- **用户ID标准化**: 转小写，移除特殊字符
- **事件类型标准化**: 统一事件类型命名
- **URL标准化**: 移除跟踪参数，标准化格式
- **IP地址脱敏**: 保留前两段，后两段用*替代
- **设备类型标准化**: 统一设备类型分类

### 3. 数据增强
- **User Agent解析**: 提取浏览器、版本、操作系统信息
- **机器人检测**: 基于User Agent和行为模式检测机器人
- **地理位置解析**: 解析国家和城市信息
- **移动设备检测**: 识别移动设备和平板设备

### 4. 数据去重
- 基于用户ID、事件类型、时间窗口进行去重
- 使用Flink状态管理跟踪重复事件
- 可配置的去重时间窗口（默认5分钟）

## 监控和日志

### 日志配置
- 应用日志：`logs/flink-data-cleaning.log`
- 错误日志：`logs/flink-data-cleaning-error.log`
- 日志级别可在 `log4j2.xml` 中配置

### 监控指标
- 处理事件数量
- 验证失败事件数量
- 去重事件数量
- 处理延迟

## 部署

### 本地开发环境
使用提供的脚本即可在本地运行：
```bash
./scripts/run-local.sh
```

### Flink集群部署
1. 打包应用：
```bash
mvn clean package
```

2. 提交到Flink集群：
```bash
flink run -c com.example.flink.DataCleaningJob target/flink-data-cleaning-1.0.0.jar \
    --source-type kafka --sink-type kafka \
    --kafka-bootstrap-servers your-kafka-servers \
    --kafka-topic user-events \
    --kafka-output-topic cleaned-user-events
```

## 扩展和定制

### 添加新的清洗规则
1. 在 `DataCleaningFunction` 中添加新的处理方法
2. 在 `CleanedUserEvent` 模型中添加新字段
3. 更新相应的测试用例

### 添加新的数据源/输出
1. 实现相应的序列化/反序列化类
2. 在 `DataCleaningJob` 中添加新的源/输出创建方法
3. 更新命令行参数解析

### 性能优化
- 调整并行度设置
- 优化检查点配置
- 使用更高效的序列化格式
- 调整内存配置

## 故障排除

### 常见问题
1. **内存不足**: 调整 `taskmanager.memory.process.size` 配置
2. **Kafka连接失败**: 检查Kafka服务状态和网络连接
3. **序列化错误**: 检查输入数据格式是否正确
4. **检查点失败**: 检查文件系统权限和磁盘空间

### 调试技巧
- 启用DEBUG日志级别查看详细信息
- 使用Flink Web UI监控作业状态
- 检查异常日志文件

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。
