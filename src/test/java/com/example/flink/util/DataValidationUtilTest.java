package com.example.flink.util;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DataValidationUtil测试类
 */
public class DataValidationUtilTest {
    
    @Test
    void testIsNullOrEmpty() {
        assertTrue(DataValidationUtil.isNullOrEmpty(null));
        assertTrue(DataValidationUtil.isNullOrEmpty(""));
        assertTrue(DataValidationUtil.isNullOrEmpty("   "));
        assertFalse(DataValidationUtil.isNullOrEmpty("test"));
        assertFalse(DataValidationUtil.isNullOrEmpty("  test  "));
    }
    
    @Test
    void testIsValidEmail() {
        assertTrue(DataValidationUtil.isValidEmail("<EMAIL>"));
        assertTrue(DataValidationUtil.isValidEmail("<EMAIL>"));
        assertFalse(DataValidationUtil.isValidEmail("invalid-email"));
        assertFalse(DataValidationUtil.isValidEmail("@example.com"));
        assertFalse(DataValidationUtil.isValidEmail("test@"));
        assertFalse(DataValidationUtil.isValidEmail(null));
        assertFalse(DataValidationUtil.isValidEmail(""));
    }
    
    @Test
    void testIsValidPhone() {
        assertTrue(DataValidationUtil.isValidPhone("13812345678"));
        assertTrue(DataValidationUtil.isValidPhone("15987654321"));
        assertFalse(DataValidationUtil.isValidPhone("12345678901")); // 不是1开头的有效号段
        assertFalse(DataValidationUtil.isValidPhone("1381234567")); // 长度不够
        assertFalse(DataValidationUtil.isValidPhone("138123456789")); // 长度过长
        assertFalse(DataValidationUtil.isValidPhone(null));
        assertFalse(DataValidationUtil.isValidPhone(""));
    }
    
    @Test
    void testIsValidUrl() {
        assertTrue(DataValidationUtil.isValidUrl("https://example.com"));
        assertTrue(DataValidationUtil.isValidUrl("http://test.org/path"));
        assertTrue(DataValidationUtil.isValidUrl("https://sub.domain.com/path?param=value"));
        assertFalse(DataValidationUtil.isValidUrl("invalid-url"));
        assertFalse(DataValidationUtil.isValidUrl("ftp://example.com"));
        assertFalse(DataValidationUtil.isValidUrl(null));
        assertFalse(DataValidationUtil.isValidUrl(""));
    }
    
    @Test
    void testIsValidIpAddress() {
        assertTrue(DataValidationUtil.isValidIpAddress("***********"));
        assertTrue(DataValidationUtil.isValidIpAddress("********"));
        assertTrue(DataValidationUtil.isValidIpAddress("***************"));
        assertFalse(DataValidationUtil.isValidIpAddress("256.1.1.1")); // 超出范围
        assertFalse(DataValidationUtil.isValidIpAddress("192.168.1")); // 格式不完整
        assertFalse(DataValidationUtil.isValidIpAddress("***********.1")); // 格式错误
        assertFalse(DataValidationUtil.isValidIpAddress(null));
        assertFalse(DataValidationUtil.isValidIpAddress(""));
    }
    
    @Test
    void testIsInRange() {
        assertTrue(DataValidationUtil.isInRange(5L, 1L, 10L));
        assertTrue(DataValidationUtil.isInRange(1L, 1L, 10L)); // 边界值
        assertTrue(DataValidationUtil.isInRange(10L, 1L, 10L)); // 边界值
        assertFalse(DataValidationUtil.isInRange(0L, 1L, 10L));
        assertFalse(DataValidationUtil.isInRange(11L, 1L, 10L));
        assertFalse(DataValidationUtil.isInRange(null, 1L, 10L));
    }
    
    @Test
    void testIsValidTimestamp() {
        LocalDateTime now = LocalDateTime.now();
        assertTrue(DataValidationUtil.isValidTimestamp(now));
        assertTrue(DataValidationUtil.isValidTimestamp(now.minusMonths(6)));
        assertTrue(DataValidationUtil.isValidTimestamp(now.plusMinutes(30)));
        
        assertFalse(DataValidationUtil.isValidTimestamp(now.minusYears(2))); // 太久以前
        assertFalse(DataValidationUtil.isValidTimestamp(now.plusHours(2))); // 太远未来
        assertFalse(DataValidationUtil.isValidTimestamp(null));
    }
    
    @Test
    void testCleanString() {
        assertEquals("test123", DataValidationUtil.cleanString("  Test123!@#  "));
        assertEquals("user_id", DataValidationUtil.cleanString("User_ID"));
        assertEquals("hello-world", DataValidationUtil.cleanString("Hello-World"));
        assertNull(DataValidationUtil.cleanString("!@#$%")); // 只有特殊字符
        assertNull(DataValidationUtil.cleanString(null));
        assertNull(DataValidationUtil.cleanString(""));
    }
    
    @Test
    void testIsValidLength() {
        assertTrue(DataValidationUtil.isValidLength("test", 1, 10));
        assertTrue(DataValidationUtil.isValidLength("test", 4, 4)); // 边界值
        assertFalse(DataValidationUtil.isValidLength("test", 5, 10));
        assertFalse(DataValidationUtil.isValidLength("test", 1, 3));
        assertTrue(DataValidationUtil.isValidLength(null, 0, 10)); // null且最小长度为0
        assertFalse(DataValidationUtil.isValidLength(null, 1, 10)); // null但最小长度不为0
    }
    
    @Test
    void testIsPositive() {
        assertTrue(DataValidationUtil.isPositive(1L));
        assertTrue(DataValidationUtil.isPositive(100L));
        assertFalse(DataValidationUtil.isPositive(0L));
        assertFalse(DataValidationUtil.isPositive(-1L));
        assertFalse(DataValidationUtil.isPositive(null));
    }
    
    @Test
    void testIsNonNegative() {
        assertTrue(DataValidationUtil.isNonNegative(0L));
        assertTrue(DataValidationUtil.isNonNegative(1L));
        assertTrue(DataValidationUtil.isNonNegative(100L));
        assertFalse(DataValidationUtil.isNonNegative(-1L));
        assertFalse(DataValidationUtil.isNonNegative(null));
    }
    
    @Test
    void testNormalizeUserId() {
        assertEquals("user_001", DataValidationUtil.normalizeUserId("  User_001  "));
        assertEquals("test-user", DataValidationUtil.normalizeUserId("Test-User"));
        assertEquals("user123", DataValidationUtil.normalizeUserId("User123!@#"));
        assertNull(DataValidationUtil.normalizeUserId("")); // 空字符串
        assertNull(DataValidationUtil.normalizeUserId("!@#")); // 只有特殊字符
        assertNull(DataValidationUtil.normalizeUserId(null));
    }
    
    @Test
    void testNormalizeEventType() {
        assertEquals("page_view", DataValidationUtil.normalizeEventType("PAGE_VIEW"));
        assertEquals("page_view", DataValidationUtil.normalizeEventType("pageview"));
        assertEquals("page_view", DataValidationUtil.normalizeEventType("page-view"));
        assertEquals("click", DataValidationUtil.normalizeEventType("CLICK"));
        assertEquals("click", DataValidationUtil.normalizeEventType("button_click"));
        assertEquals("form_submit", DataValidationUtil.normalizeEventType("form-submit"));
        assertEquals("custom_event", DataValidationUtil.normalizeEventType("custom_event"));
        assertNull(DataValidationUtil.normalizeEventType(null));
        assertNull(DataValidationUtil.normalizeEventType(""));
    }
    
    @Test
    void testIsValidSessionId() {
        assertTrue(DataValidationUtil.isValidSessionId("sess_abc123def456"));
        assertTrue(DataValidationUtil.isValidSessionId("session-123456789"));
        assertTrue(DataValidationUtil.isValidSessionId("1234567890abcdef"));
        assertFalse(DataValidationUtil.isValidSessionId("short")); // 太短
        assertFalse(DataValidationUtil.isValidSessionId("sess_123!@#")); // 包含特殊字符
        assertFalse(DataValidationUtil.isValidSessionId(null));
        assertFalse(DataValidationUtil.isValidSessionId(""));
    }
    
    @Test
    void testCleanUserAgent() {
        String longUserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
        assertEquals(longUserAgent, DataValidationUtil.cleanUserAgent(longUserAgent));
        
        // 测试过长的用户代理字符串
        String veryLongUserAgent = "a".repeat(600);
        String cleaned = DataValidationUtil.cleanUserAgent(veryLongUserAgent);
        assertEquals(500, cleaned.length());
        
        assertNull(DataValidationUtil.cleanUserAgent(null));
        assertNull(DataValidationUtil.cleanUserAgent(""));
    }
    
    @Test
    void testIsValidDuration() {
        assertTrue(DataValidationUtil.isValidDuration(0L));
        assertTrue(DataValidationUtil.isValidDuration(3600L)); // 1小时
        assertTrue(DataValidationUtil.isValidDuration(86400L)); // 24小时
        assertFalse(DataValidationUtil.isValidDuration(-1L)); // 负数
        assertFalse(DataValidationUtil.isValidDuration(86401L)); // 超过24小时
        assertTrue(DataValidationUtil.isValidDuration(null)); // null是可选的
    }
}
