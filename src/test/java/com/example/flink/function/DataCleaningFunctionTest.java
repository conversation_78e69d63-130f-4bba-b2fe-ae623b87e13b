package com.example.flink.function;

import com.example.flink.model.CleanedUserEvent;
import com.example.flink.model.UserEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DataCleaningFunction测试类
 */
public class DataCleaningFunctionTest {
    
    private DataCleaningFunction cleaningFunction;
    
    @BeforeEach
    void setUp() {
        cleaningFunction = new DataCleaningFunction();
    }
    
    @Test
    void testBasicCleaning() throws Exception {
        // 准备测试数据
        UserEvent userEvent = new UserEvent();
        userEvent.setUserId("  USER_001  ");
        userEvent.setEventType("  PAGE_VIEW  ");
        userEvent.setTimestamp(LocalDateTime.now());
        userEvent.setSessionId("  sess_123  ");
        userEvent.setPageUrl("https://example.com/home?utm_source=google");
        userEvent.setIpAddress("*************");
        userEvent.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        userEvent.setDeviceType("DESKTOP");
        userEvent.setLocation("Beijing,China");
        userEvent.setDuration(45L);
        
        // 执行清洗
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("user_001", result.getUserId());
        assertEquals("page_view", result.getEventType());
        assertEquals("sess_123", result.getSessionId());
        assertNotNull(result.getNormalizedUrl());
        assertNotNull(result.getMaskedIp());
        assertEquals("desktop", result.getDeviceType());
        assertNotNull(result.getProcessingTimestamp());
    }
    
    @Test
    void testUrlNormalization() throws Exception {
        UserEvent userEvent = createBasicUserEvent();
        userEvent.setPageUrl("https://example.com/home?utm_source=google&utm_medium=cpc&ref=homepage");
        
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        assertNotNull(result.getNormalizedUrl());
        // URL应该被标准化，移除跟踪参数
        assertFalse(result.getNormalizedUrl().contains("utm_source"));
    }
    
    @Test
    void testIpMasking() throws Exception {
        UserEvent userEvent = createBasicUserEvent();
        userEvent.setIpAddress("*************");
        
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        assertNotNull(result.getMaskedIp());
        assertEquals("192.168.*.*", result.getMaskedIp());
    }
    
    @Test
    void testUserAgentParsing() throws Exception {
        UserEvent userEvent = createBasicUserEvent();
        userEvent.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");
        
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        assertEquals("Chrome", result.getBrowser());
        assertNotNull(result.getBrowserVersion());
        assertEquals(Boolean.FALSE, result.getIsMobile());
    }
    
    @Test
    void testMobileDeviceDetection() throws Exception {
        UserEvent userEvent = createBasicUserEvent();
        userEvent.setUserAgent("Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15");
        userEvent.setDeviceType("mobile");
        
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        assertEquals("mobile", result.getDeviceType());
        assertEquals(Boolean.TRUE, result.getIsMobile());
    }
    
    @Test
    void testBotDetection() throws Exception {
        UserEvent userEvent = createBasicUserEvent();
        userEvent.setUserAgent("Googlebot/2.1 (+http://www.google.com/bot.html)");
        
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        assertEquals(Boolean.TRUE, result.getIsBot());
    }
    
    @Test
    void testLocationParsing() throws Exception {
        UserEvent userEvent = createBasicUserEvent();
        userEvent.setLocation("Beijing,China");
        
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        assertEquals("Beijing", result.getCountry());
        assertEquals("China", result.getCity());
    }
    
    @Test
    void testAnomalyHandling() throws Exception {
        UserEvent userEvent = createBasicUserEvent();
        userEvent.setDuration(-10L); // 异常的负数持续时间
        
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        assertEquals(0L, result.getDuration()); // 应该被修正为0
    }
    
    @Test
    void testDeviceTypeNormalization() throws Exception {
        UserEvent userEvent = createBasicUserEvent();
        userEvent.setDeviceType("SMARTPHONE");
        
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        assertEquals("mobile", result.getDeviceType());
    }
    
    @Test
    void testNullHandling() throws Exception {
        UserEvent userEvent = new UserEvent();
        userEvent.setUserId("user_001");
        userEvent.setEventType("page_view");
        userEvent.setTimestamp(LocalDateTime.now());
        userEvent.setSessionId("sess_123");
        // 其他字段为null
        
        CleanedUserEvent result = cleaningFunction.map(userEvent);
        
        assertNotNull(result);
        assertEquals("user_001", result.getUserId());
        assertEquals("page_view", result.getEventType());
        assertNotNull(result.getProcessingTimestamp());
    }
    
    /**
     * 创建基础的UserEvent用于测试
     */
    private UserEvent createBasicUserEvent() {
        UserEvent userEvent = new UserEvent();
        userEvent.setUserId("user_001");
        userEvent.setEventType("page_view");
        userEvent.setTimestamp(LocalDateTime.now());
        userEvent.setSessionId("sess_123");
        userEvent.setPageUrl("https://example.com/home");
        userEvent.setIpAddress("*************");
        userEvent.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
        userEvent.setDeviceType("desktop");
        userEvent.setLocation("Beijing,China");
        userEvent.setDuration(45L);
        return userEvent;
    }
}
