package com.example.flink;

import com.example.flink.function.DataCleaningFunction;
import com.example.flink.function.DataValidationFunction;
import com.example.flink.function.DeduplicationFunction;
import com.example.flink.model.CleanedUserEvent;
import com.example.flink.model.UserEvent;
import com.example.flink.serialization.UserEventDeserializationSchema;
import com.example.flink.serialization.CleanedUserEventSerializationSchema;
import org.apache.flink.api.common.eventtime.WatermarkStrategy;
import org.apache.flink.api.common.serialization.SimpleStringSchema;
import org.apache.flink.connector.file.sink.FileSink;
import org.apache.flink.connector.file.src.FileSource;
import org.apache.flink.connector.file.src.reader.TextLineInputFormat;
import org.apache.flink.connector.kafka.sink.KafkaSink;
import org.apache.flink.connector.kafka.source.KafkaSource;
import org.apache.flink.connector.kafka.source.enumerator.initializer.OffsetsInitializer;
import org.apache.flink.core.fs.Path;
import org.apache.flink.formats.json.JsonSerializationSchema;
import org.apache.flink.streaming.api.datastream.DataStream;
import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.streaming.api.functions.sink.filesystem.rollingpolicies.DefaultRollingPolicy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;

/**
 * Flink数据清洗作业主类
 * 实现完整的数据清洗流水线：数据读取 -> 验证 -> 清洗 -> 去重 -> 输出
 */
public class DataCleaningJob {
    
    private static final Logger logger = LoggerFactory.getLogger(DataCleaningJob.class);
    
    public static void main(String[] args) throws Exception {
        // 创建执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置并行度
        env.setParallelism(4);
        
        // 启用检查点
        env.enableCheckpointing(60000); // 每分钟一次检查点
        
        logger.info("Starting Flink Data Cleaning Job...");
        
        // 解析命令行参数
        JobParameters params = parseArguments(args);
        
        // 创建数据源
        DataStream<UserEvent> sourceStream = createDataSource(env, params);
        
        // 执行数据清洗流水线
        DataStream<CleanedUserEvent> cleanedStream = buildDataCleaningPipeline(sourceStream);
        
        // 创建数据输出
        createDataSink(cleanedStream, params);
        
        // 执行作业
        env.execute("Flink Data Cleaning Job");
        
        logger.info("Flink Data Cleaning Job completed successfully");
    }
    
    /**
     * 创建数据源
     */
    private static DataStream<UserEvent> createDataSource(StreamExecutionEnvironment env, JobParameters params) {
        switch (params.getSourceType()) {
            case "kafka":
                return createKafkaSource(env, params);
            case "file":
                return createFileSource(env, params);
            default:
                throw new IllegalArgumentException("Unsupported source type: " + params.getSourceType());
        }
    }
    
    /**
     * 创建Kafka数据源
     */
    private static DataStream<UserEvent> createKafkaSource(StreamExecutionEnvironment env, JobParameters params) {
        logger.info("Creating Kafka source with topic: {}", params.getKafkaTopic());
        
        KafkaSource<UserEvent> kafkaSource = KafkaSource.<UserEvent>builder()
                .setBootstrapServers(params.getKafkaBootstrapServers())
                .setTopics(params.getKafkaTopic())
                .setGroupId(params.getKafkaGroupId())
                .setStartingOffsets(OffsetsInitializer.latest())
                .setValueOnlyDeserializer(new UserEventDeserializationSchema())
                .build();
        
        return env.fromSource(kafkaSource, WatermarkStrategy.noWatermarks(), "Kafka Source");
    }
    
    /**
     * 创建文件数据源
     */
    private static DataStream<UserEvent> createFileSource(StreamExecutionEnvironment env, JobParameters params) {
        logger.info("Creating file source with path: {}", params.getInputPath());
        
        FileSource<String> fileSource = FileSource.forRecordStreamFormat(
                new TextLineInputFormat(),
                new Path(params.getInputPath())
        ).build();
        
        return env.fromSource(fileSource, WatermarkStrategy.noWatermarks(), "File Source")
                .map(new UserEventDeserializationSchema()::deserialize)
                .filter(event -> event != null);
    }
    
    /**
     * 构建数据清洗流水线
     */
    private static DataStream<CleanedUserEvent> buildDataCleaningPipeline(DataStream<UserEvent> sourceStream) {
        logger.info("Building data cleaning pipeline...");
        
        return sourceStream
                // 1. 数据验证和过滤
                .filter(new DataValidationFunction())
                .name("Data Validation")
                
                // 2. 数据清洗和转换
                .map(new DataCleaningFunction())
                .name("Data Cleaning")
                
                // 3. 按用户ID分组进行去重
                .keyBy(CleanedUserEvent::getUserId)
                .process(new DeduplicationFunction())
                .name("Data Deduplication");
    }
    
    /**
     * 创建数据输出
     */
    private static void createDataSink(DataStream<CleanedUserEvent> cleanedStream, JobParameters params) {
        switch (params.getSinkType()) {
            case "kafka":
                createKafkaSink(cleanedStream, params);
                break;
            case "file":
                createFileSink(cleanedStream, params);
                break;
            case "both":
                createKafkaSink(cleanedStream, params);
                createFileSink(cleanedStream, params);
                break;
            default:
                throw new IllegalArgumentException("Unsupported sink type: " + params.getSinkType());
        }
    }
    
    /**
     * 创建Kafka输出
     */
    private static void createKafkaSink(DataStream<CleanedUserEvent> cleanedStream, JobParameters params) {
        logger.info("Creating Kafka sink with topic: {}", params.getKafkaOutputTopic());
        
        KafkaSink<CleanedUserEvent> kafkaSink = KafkaSink.<CleanedUserEvent>builder()
                .setBootstrapServers(params.getKafkaBootstrapServers())
                .setRecordSerializer(new CleanedUserEventSerializationSchema(params.getKafkaOutputTopic()))
                .build();
        
        cleanedStream.sinkTo(kafkaSink).name("Kafka Sink");
    }
    
    /**
     * 创建文件输出
     */
    private static void createFileSink(DataStream<CleanedUserEvent> cleanedStream, JobParameters params) {
        logger.info("Creating file sink with path: {}", params.getOutputPath());
        
        FileSink<CleanedUserEvent> fileSink = FileSink
                .forRowFormat(new Path(params.getOutputPath()), 
                             new JsonSerializationSchema<CleanedUserEvent>())
                .withRollingPolicy(
                    DefaultRollingPolicy.builder()
                        .withRolloverInterval(Duration.ofMinutes(15))
                        .withInactivityInterval(Duration.ofMinutes(5))
                        .withMaxPartSize(1024 * 1024 * 128) // 128MB
                        .build())
                .build();
        
        cleanedStream.sinkTo(fileSink).name("File Sink");
    }
    
    /**
     * 解析命令行参数
     */
    private static JobParameters parseArguments(String[] args) {
        JobParameters params = new JobParameters();
        
        for (int i = 0; i < args.length; i += 2) {
            if (i + 1 >= args.length) {
                break;
            }
            
            String key = args[i];
            String value = args[i + 1];
            
            switch (key) {
                case "--source-type":
                    params.setSourceType(value);
                    break;
                case "--sink-type":
                    params.setSinkType(value);
                    break;
                case "--kafka-bootstrap-servers":
                    params.setKafkaBootstrapServers(value);
                    break;
                case "--kafka-topic":
                    params.setKafkaTopic(value);
                    break;
                case "--kafka-group-id":
                    params.setKafkaGroupId(value);
                    break;
                case "--kafka-output-topic":
                    params.setKafkaOutputTopic(value);
                    break;
                case "--input-path":
                    params.setInputPath(value);
                    break;
                case "--output-path":
                    params.setOutputPath(value);
                    break;
                default:
                    logger.warn("Unknown parameter: {}", key);
            }
        }
        
        // 设置默认值
        if (params.getSourceType() == null) {
            params.setSourceType("file");
        }
        if (params.getSinkType() == null) {
            params.setSinkType("file");
        }
        if (params.getInputPath() == null) {
            params.setInputPath("input/user_events.json");
        }
        if (params.getOutputPath() == null) {
            params.setOutputPath("output/cleaned_events");
        }
        if (params.getKafkaBootstrapServers() == null) {
            params.setKafkaBootstrapServers("localhost:9092");
        }
        if (params.getKafkaTopic() == null) {
            params.setKafkaTopic("user-events");
        }
        if (params.getKafkaGroupId() == null) {
            params.setKafkaGroupId("data-cleaning-group");
        }
        if (params.getKafkaOutputTopic() == null) {
            params.setKafkaOutputTopic("cleaned-user-events");
        }
        
        logger.info("Job parameters: {}", params);
        return params;
    }
    
    /**
     * 作业参数类
     */
    public static class JobParameters {
        private String sourceType;
        private String sinkType;
        private String kafkaBootstrapServers;
        private String kafkaTopic;
        private String kafkaGroupId;
        private String kafkaOutputTopic;
        private String inputPath;
        private String outputPath;
        
        // Getter和Setter方法
        public String getSourceType() { return sourceType; }
        public void setSourceType(String sourceType) { this.sourceType = sourceType; }
        
        public String getSinkType() { return sinkType; }
        public void setSinkType(String sinkType) { this.sinkType = sinkType; }
        
        public String getKafkaBootstrapServers() { return kafkaBootstrapServers; }
        public void setKafkaBootstrapServers(String kafkaBootstrapServers) { 
            this.kafkaBootstrapServers = kafkaBootstrapServers; 
        }
        
        public String getKafkaTopic() { return kafkaTopic; }
        public void setKafkaTopic(String kafkaTopic) { this.kafkaTopic = kafkaTopic; }
        
        public String getKafkaGroupId() { return kafkaGroupId; }
        public void setKafkaGroupId(String kafkaGroupId) { this.kafkaGroupId = kafkaGroupId; }
        
        public String getKafkaOutputTopic() { return kafkaOutputTopic; }
        public void setKafkaOutputTopic(String kafkaOutputTopic) { 
            this.kafkaOutputTopic = kafkaOutputTopic; 
        }
        
        public String getInputPath() { return inputPath; }
        public void setInputPath(String inputPath) { this.inputPath = inputPath; }
        
        public String getOutputPath() { return outputPath; }
        public void setOutputPath(String outputPath) { this.outputPath = outputPath; }
        
        @Override
        public String toString() {
            return "JobParameters{" +
                    "sourceType='" + sourceType + '\'' +
                    ", sinkType='" + sinkType + '\'' +
                    ", kafkaBootstrapServers='" + kafkaBootstrapServers + '\'' +
                    ", kafkaTopic='" + kafkaTopic + '\'' +
                    ", kafkaGroupId='" + kafkaGroupId + '\'' +
                    ", kafkaOutputTopic='" + kafkaOutputTopic + '\'' +
                    ", inputPath='" + inputPath + '\'' +
                    ", outputPath='" + outputPath + '\'' +
                    '}';
        }
    }
}
