package com.example.flink.serialization;

import com.example.flink.model.CleanedUserEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.flink.connector.kafka.sink.KafkaRecordSerializationSchema;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Nullable;
import java.nio.charset.StandardCharsets;

/**
 * CleanedUserEvent序列化模式
 * 用于将CleanedUserEvent对象序列化为Kafka消息
 */
public class CleanedUserEventSerializationSchema implements KafkaRecordSerializationSchema<CleanedUserEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(CleanedUserEventSerializationSchema.class);
    
    private final String topic;
    private transient ObjectMapper objectMapper;
    
    public CleanedUserEventSerializationSchema(String topic) {
        this.topic = topic;
    }
    
    @Override
    public void open(SerializationSchema.InitializationContext context, KafkaSinkContext sinkContext) 
            throws Exception {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        // 配置时间格式
        objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
    }
    
    @Override
    public ProducerRecord<byte[], byte[]> serialize(CleanedUserEvent element, KafkaSinkContext context, 
                                                   Long timestamp) {
        try {
            // 确保ObjectMapper已初始化
            if (objectMapper == null) {
                objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                objectMapper.configure(com.fasterxml.jackson.databind.SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
            }
            
            // 序列化为JSON字符串
            String jsonString = objectMapper.writeValueAsString(element);
            byte[] value = jsonString.getBytes(StandardCharsets.UTF_8);
            
            // 使用用户ID作为消息键，确保同一用户的消息发送到同一分区
            byte[] key = null;
            if (element.getUserId() != null) {
                key = element.getUserId().getBytes(StandardCharsets.UTF_8);
            }
            
            return new ProducerRecord<>(topic, key, value);
            
        } catch (Exception e) {
            logger.error("Failed to serialize CleanedUserEvent: {}", element, e);
            // 返回null会导致消息被丢弃
            return null;
        }
    }
}
