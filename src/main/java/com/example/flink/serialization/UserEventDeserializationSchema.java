package com.example.flink.serialization;

import com.example.flink.model.UserEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.flink.api.common.serialization.DeserializationSchema;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * UserEvent反序列化模式
 * 用于将JSON字符串反序列化为UserEvent对象
 */
public class UserEventDeserializationSchema implements DeserializationSchema<UserEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(UserEventDeserializationSchema.class);
    
    private transient ObjectMapper objectMapper;
    
    @Override
    public void open(InitializationContext context) throws Exception {
        objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        // 忽略未知属性，提高容错性
        objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 忽略空值
        objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
    }
    
    @Override
    public UserEvent deserialize(byte[] message) throws IOException {
        if (message == null || message.length == 0) {
            logger.warn("Received empty message");
            return null;
        }
        
        try {
            String jsonString = new String(message, StandardCharsets.UTF_8);
            return deserialize(jsonString);
        } catch (Exception e) {
            logger.error("Failed to deserialize message: {}", new String(message, StandardCharsets.UTF_8), e);
            return null;
        }
    }
    
    /**
     * 从JSON字符串反序列化UserEvent
     */
    public UserEvent deserialize(String jsonString) throws IOException {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            logger.warn("Received empty JSON string");
            return null;
        }
        
        try {
            // 确保ObjectMapper已初始化
            if (objectMapper == null) {
                objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
            }
            
            UserEvent event = objectMapper.readValue(jsonString.trim(), UserEvent.class);
            
            // 基础验证
            if (event != null && !event.isValid()) {
                logger.warn("Deserialized invalid UserEvent: {}", jsonString);
                return null;
            }
            
            return event;
            
        } catch (Exception e) {
            logger.error("Failed to deserialize JSON string: {}", jsonString, e);
            return null;
        }
    }
    
    @Override
    public boolean isEndOfStream(UserEvent nextElement) {
        return false;
    }
    
    @Override
    public TypeInformation<UserEvent> getProducedType() {
        return TypeInformation.of(UserEvent.class);
    }
}
