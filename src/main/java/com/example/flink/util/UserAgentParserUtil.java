package com.example.flink.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * User Agent解析工具类
 * 用于解析用户代理字符串，提取浏览器、版本、操作系统等信息
 */
public class UserAgentParserUtil {
    
    // 浏览器检测正则表达式
    private static final Pattern CHROME_PATTERN = Pattern.compile("Chrome/([\\d.]+)");
    private static final Pattern FIREFOX_PATTERN = Pattern.compile("Firefox/([\\d.]+)");
    private static final Pattern SAFARI_PATTERN = Pattern.compile("Version/([\\d.]+).*Safari");
    private static final Pattern EDGE_PATTERN = Pattern.compile("Edge/([\\d.]+)");
    private static final Pattern IE_PATTERN = Pattern.compile("MSIE ([\\d.]+)");
    private static final Pattern OPERA_PATTERN = Pattern.compile("Opera/([\\d.]+)");
    
    // 移动设备检测正则表达式
    private static final Pattern MOBILE_PATTERN = Pattern.compile(
        "(?i).*(mobile|android|iphone|ipad|blackberry|windows phone|webos).*"
    );
    
    // 操作系统检测正则表达式
    private static final Pattern WINDOWS_PATTERN = Pattern.compile("Windows NT ([\\d.]+)");
    private static final Pattern MAC_PATTERN = Pattern.compile("Mac OS X ([\\d_]+)");
    private static final Pattern LINUX_PATTERN = Pattern.compile("Linux");
    private static final Pattern ANDROID_PATTERN = Pattern.compile("Android ([\\d.]+)");
    private static final Pattern IOS_PATTERN = Pattern.compile("OS ([\\d_]+)");
    
    // 机器人检测正则表达式
    private static final Pattern BOT_PATTERN = Pattern.compile(
        "(?i).*(bot|crawler|spider|scraper|wget|curl|googlebot|bingbot|slurp).*"
    );
    
    /**
     * 解析User Agent字符串
     */
    public static ParseResult parse(String userAgent) {
        if (userAgent == null || userAgent.trim().isEmpty()) {
            return new ParseResult();
        }
        
        String ua = userAgent.trim();
        ParseResult result = new ParseResult();
        
        // 解析浏览器信息
        parseBrowser(ua, result);
        
        // 解析操作系统信息
        parseOperatingSystem(ua, result);
        
        // 检测设备类型
        parseDeviceType(ua, result);
        
        // 检测是否为机器人
        result.setBot(BOT_PATTERN.matcher(ua).find());
        
        return result;
    }
    
    /**
     * 解析浏览器信息
     */
    private static void parseBrowser(String userAgent, ParseResult result) {
        Matcher matcher;
        
        // Chrome (需要在Safari之前检测，因为Chrome的UA也包含Safari)
        matcher = CHROME_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            result.setBrowser("Chrome");
            result.setBrowserVersion(matcher.group(1));
            return;
        }
        
        // Edge
        matcher = EDGE_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            result.setBrowser("Edge");
            result.setBrowserVersion(matcher.group(1));
            return;
        }
        
        // Firefox
        matcher = FIREFOX_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            result.setBrowser("Firefox");
            result.setBrowserVersion(matcher.group(1));
            return;
        }
        
        // Safari
        matcher = SAFARI_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            result.setBrowser("Safari");
            result.setBrowserVersion(matcher.group(1));
            return;
        }
        
        // Internet Explorer
        matcher = IE_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            result.setBrowser("Internet Explorer");
            result.setBrowserVersion(matcher.group(1));
            return;
        }
        
        // Opera
        matcher = OPERA_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            result.setBrowser("Opera");
            result.setBrowserVersion(matcher.group(1));
            return;
        }
        
        // 未知浏览器
        result.setBrowser("Unknown");
        result.setBrowserVersion("Unknown");
    }
    
    /**
     * 解析操作系统信息
     */
    private static void parseOperatingSystem(String userAgent, ParseResult result) {
        Matcher matcher;
        
        // Windows
        matcher = WINDOWS_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            result.setOperatingSystem("Windows");
            result.setOsVersion(matcher.group(1));
            return;
        }
        
        // Android
        matcher = ANDROID_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            result.setOperatingSystem("Android");
            result.setOsVersion(matcher.group(1));
            return;
        }
        
        // iOS
        if (userAgent.contains("iPhone") || userAgent.contains("iPad")) {
            matcher = IOS_PATTERN.matcher(userAgent);
            if (matcher.find()) {
                result.setOperatingSystem("iOS");
                result.setOsVersion(matcher.group(1).replace("_", "."));
                return;
            }
        }
        
        // Mac OS X
        matcher = MAC_PATTERN.matcher(userAgent);
        if (matcher.find()) {
            result.setOperatingSystem("Mac OS X");
            result.setOsVersion(matcher.group(1).replace("_", "."));
            return;
        }
        
        // Linux
        if (LINUX_PATTERN.matcher(userAgent).find()) {
            result.setOperatingSystem("Linux");
            result.setOsVersion("Unknown");
            return;
        }
        
        // 未知操作系统
        result.setOperatingSystem("Unknown");
        result.setOsVersion("Unknown");
    }
    
    /**
     * 解析设备类型
     */
    private static void parseDeviceType(String userAgent, ParseResult result) {
        // 检测移动设备
        boolean isMobile = MOBILE_PATTERN.matcher(userAgent).find();
        result.setMobile(isMobile);
        
        if (isMobile) {
            if (userAgent.contains("iPad") || userAgent.contains("tablet")) {
                result.setDeviceType("tablet");
            } else {
                result.setDeviceType("mobile");
            }
        } else {
            result.setDeviceType("desktop");
        }
    }
    
    /**
     * 解析结果类
     */
    public static class ParseResult {
        private String browser = "Unknown";
        private String browserVersion = "Unknown";
        private String operatingSystem = "Unknown";
        private String osVersion = "Unknown";
        private String deviceType = "Unknown";
        private boolean isMobile = false;
        private boolean isBot = false;
        
        // Getter和Setter方法
        public String getBrowser() { return browser; }
        public void setBrowser(String browser) { this.browser = browser; }
        
        public String getBrowserVersion() { return browserVersion; }
        public void setBrowserVersion(String browserVersion) { this.browserVersion = browserVersion; }
        
        public String getOperatingSystem() { return operatingSystem; }
        public void setOperatingSystem(String operatingSystem) { this.operatingSystem = operatingSystem; }
        
        public String getOsVersion() { return osVersion; }
        public void setOsVersion(String osVersion) { this.osVersion = osVersion; }
        
        public String getDeviceType() { return deviceType; }
        public void setDeviceType(String deviceType) { this.deviceType = deviceType; }
        
        public boolean isMobile() { return isMobile; }
        public void setMobile(boolean mobile) { isMobile = mobile; }
        
        public boolean isBot() { return isBot; }
        public void setBot(boolean bot) { isBot = bot; }
        
        @Override
        public String toString() {
            return "ParseResult{" +
                    "browser='" + browser + '\'' +
                    ", browserVersion='" + browserVersion + '\'' +
                    ", operatingSystem='" + operatingSystem + '\'' +
                    ", osVersion='" + osVersion + '\'' +
                    ", deviceType='" + deviceType + '\'' +
                    ", isMobile=" + isMobile +
                    ", isBot=" + isBot +
                    '}';
        }
    }
    
    /**
     * 简化的浏览器检测（只返回主要浏览器类型）
     */
    public static String getSimpleBrowserType(String userAgent) {
        if (userAgent == null || userAgent.trim().isEmpty()) {
            return "Unknown";
        }
        
        String ua = userAgent.toLowerCase();
        
        if (ua.contains("chrome") && !ua.contains("edge")) {
            return "Chrome";
        } else if (ua.contains("firefox")) {
            return "Firefox";
        } else if (ua.contains("safari") && !ua.contains("chrome")) {
            return "Safari";
        } else if (ua.contains("edge")) {
            return "Edge";
        } else if (ua.contains("msie") || ua.contains("trident")) {
            return "Internet Explorer";
        } else if (ua.contains("opera")) {
            return "Opera";
        } else {
            return "Unknown";
        }
    }
    
    /**
     * 检测是否为移动设备
     */
    public static boolean isMobileDevice(String userAgent) {
        if (userAgent == null || userAgent.trim().isEmpty()) {
            return false;
        }
        
        return MOBILE_PATTERN.matcher(userAgent).find();
    }
    
    /**
     * 检测是否为机器人
     */
    public static boolean isBot(String userAgent) {
        if (userAgent == null || userAgent.trim().isEmpty()) {
            return false;
        }
        
        return BOT_PATTERN.matcher(userAgent).find();
    }
}
