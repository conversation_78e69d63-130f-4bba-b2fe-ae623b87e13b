package com.example.flink.util;

import java.util.regex.Pattern;

/**
 * IP地址脱敏工具类
 * 提供IP地址脱敏和匿名化处理功能
 */
public class IpMaskingUtil {
    
    // IPv4地址格式验证
    private static final Pattern IPV4_PATTERN = Pattern.compile(
        "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    // IPv6地址格式验证（简化版）
    private static final Pattern IPV6_PATTERN = Pattern.compile(
        "^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$"
    );
    
    /**
     * 对IP地址进行脱敏处理
     * IPv4: 保留前两段，后两段用*替代 (例如: 192.168.*.*)
     * IPv6: 保留前四段，后四段用*替代
     */
    public static String maskIpAddress(String ipAddress) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return null;
        }
        
        String trimmedIp = ipAddress.trim();
        
        if (isIPv4(trimmedIp)) {
            return maskIPv4(trimmedIp);
        } else if (isIPv6(trimmedIp)) {
            return maskIPv6(trimmedIp);
        } else {
            // 如果不是有效的IP地址格式，返回通用脱敏
            return "***.***.***.**";
        }
    }
    
    /**
     * 检查是否为IPv4地址
     */
    private static boolean isIPv4(String ip) {
        return IPV4_PATTERN.matcher(ip).matches();
    }
    
    /**
     * 检查是否为IPv6地址
     */
    private static boolean isIPv6(String ip) {
        return IPV6_PATTERN.matcher(ip).matches();
    }
    
    /**
     * 对IPv4地址进行脱敏
     */
    private static String maskIPv4(String ipv4) {
        String[] parts = ipv4.split("\\.");
        if (parts.length != 4) {
            return "***.***.***.**";
        }
        
        // 保留前两段，后两段用*替代
        return parts[0] + "." + parts[1] + ".*.*";
    }
    
    /**
     * 对IPv6地址进行脱敏
     */
    private static String maskIPv6(String ipv6) {
        String[] parts = ipv6.split(":");
        if (parts.length != 8) {
            return "****:****:****:****:*:*:*:*";
        }
        
        // 保留前四段，后四段用*替代
        StringBuilder masked = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            masked.append(parts[i]);
            if (i < 3) {
                masked.append(":");
            }
        }
        masked.append(":*:*:*:*");
        
        return masked.toString();
    }
    
    /**
     * 获取IP地址的网络段（用于地理位置分析等）
     * IPv4: 返回前三段 (例如: 192.168.1)
     * IPv6: 返回前六段
     */
    public static String getNetworkSegment(String ipAddress) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return null;
        }
        
        String trimmedIp = ipAddress.trim();
        
        if (isIPv4(trimmedIp)) {
            String[] parts = trimmedIp.split("\\.");
            if (parts.length >= 3) {
                return parts[0] + "." + parts[1] + "." + parts[2];
            }
        } else if (isIPv6(trimmedIp)) {
            String[] parts = trimmedIp.split(":");
            if (parts.length >= 6) {
                StringBuilder segment = new StringBuilder();
                for (int i = 0; i < 6; i++) {
                    segment.append(parts[i]);
                    if (i < 5) {
                        segment.append(":");
                    }
                }
                return segment.toString();
            }
        }
        
        return null;
    }
    
    /**
     * 检查IP地址是否为内网地址
     */
    public static boolean isPrivateIP(String ipAddress) {
        if (!isIPv4(ipAddress)) {
            return false;
        }
        
        String[] parts = ipAddress.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        try {
            int first = Integer.parseInt(parts[0]);
            int second = Integer.parseInt(parts[1]);
            
            // 10.0.0.0/8
            if (first == 10) {
                return true;
            }
            
            // **********/12
            if (first == 172 && second >= 16 && second <= 31) {
                return true;
            }
            
            // ***********/16
            if (first == 192 && second == 168) {
                return true;
            }
            
            // *********/8 (localhost)
            if (first == 127) {
                return true;
            }
            
        } catch (NumberFormatException e) {
            return false;
        }
        
        return false;
    }
    
    /**
     * 生成IP地址的哈希值（用于统计分析，保护隐私）
     */
    public static String hashIpAddress(String ipAddress) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return null;
        }
        
        // 使用简单的哈希算法
        int hash = ipAddress.hashCode();
        return "hash_" + Math.abs(hash);
    }
    
    /**
     * 根据脱敏级别对IP地址进行不同程度的脱敏
     */
    public static String maskIpAddressWithLevel(String ipAddress, MaskingLevel level) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return null;
        }
        
        switch (level) {
            case LOW:
                // 低级脱敏：只隐藏最后一段
                return maskLastSegment(ipAddress);
            case MEDIUM:
                // 中级脱敏：隐藏后两段
                return maskIpAddress(ipAddress);
            case HIGH:
                // 高级脱敏：完全哈希化
                return hashIpAddress(ipAddress);
            default:
                return maskIpAddress(ipAddress);
        }
    }
    
    /**
     * 只脱敏IP地址的最后一段
     */
    private static String maskLastSegment(String ipAddress) {
        if (isIPv4(ipAddress)) {
            String[] parts = ipAddress.split("\\.");
            if (parts.length == 4) {
                return parts[0] + "." + parts[1] + "." + parts[2] + ".*";
            }
        } else if (isIPv6(ipAddress)) {
            String[] parts = ipAddress.split(":");
            if (parts.length == 8) {
                StringBuilder masked = new StringBuilder();
                for (int i = 0; i < 7; i++) {
                    masked.append(parts[i]);
                    if (i < 6) {
                        masked.append(":");
                    }
                }
                masked.append(":*");
                return masked.toString();
            }
        }
        
        return ipAddress;
    }
    
    /**
     * 脱敏级别枚举
     */
    public enum MaskingLevel {
        LOW,    // 低级脱敏
        MEDIUM, // 中级脱敏
        HIGH    // 高级脱敏
    }
}
