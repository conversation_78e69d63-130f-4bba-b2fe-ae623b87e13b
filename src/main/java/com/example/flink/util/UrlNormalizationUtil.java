package com.example.flink.util;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * URL标准化工具类
 * 提供URL清洗、标准化和规范化功能
 */
public class UrlNormalizationUtil {
    
    // 需要移除的常见查询参数
    private static final String[] TRACKING_PARAMS = {
        "utm_source", "utm_medium", "utm_campaign", "utm_term", "utm_content",
        "gclid", "fbclid", "msclkid", "_ga", "_gid", "ref", "referrer",
        "source", "medium", "campaign", "content", "term"
    };
    
    /**
     * 标准化URL
     * 包括协议标准化、域名小写化、路径标准化、查询参数排序等
     */
    public static String normalize(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }
        
        try {
            String trimmedUrl = url.trim();
            
            // 添加协议前缀（如果缺失）
            if (!trimmedUrl.startsWith("http://") && !trimmedUrl.startsWith("https://")) {
                trimmedUrl = "https://" + trimmedUrl;
            }
            
            URL urlObj = new URL(trimmedUrl);
            
            // 标准化各个组件
            String protocol = normalizeProtocol(urlObj.getProtocol());
            String host = normalizeHost(urlObj.getHost());
            int port = normalizePort(urlObj.getPort(), protocol);
            String path = normalizePath(urlObj.getPath());
            String query = normalizeQuery(urlObj.getQuery());
            
            // 重新构建URL
            StringBuilder normalized = new StringBuilder();
            normalized.append(protocol).append("://").append(host);
            
            if (port != -1) {
                normalized.append(":").append(port);
            }
            
            normalized.append(path);
            
            if (query != null && !query.isEmpty()) {
                normalized.append("?").append(query);
            }
            
            return normalized.toString();
            
        } catch (MalformedURLException e) {
            // 如果URL格式无效，返回清理后的原始URL
            return cleanInvalidUrl(url);
        }
    }
    
    /**
     * 标准化协议
     */
    private static String normalizeProtocol(String protocol) {
        if (protocol == null) {
            return "https";
        }
        return protocol.toLowerCase();
    }
    
    /**
     * 标准化主机名
     */
    private static String normalizeHost(String host) {
        if (host == null) {
            return "";
        }
        
        String normalized = host.toLowerCase().trim();
        
        // 移除www前缀（可选，根据业务需求决定）
        if (normalized.startsWith("www.")) {
            normalized = normalized.substring(4);
        }
        
        return normalized;
    }
    
    /**
     * 标准化端口号
     */
    private static int normalizePort(int port, String protocol) {
        // 移除默认端口号
        if ((port == 80 && "http".equals(protocol)) || 
            (port == 443 && "https".equals(protocol))) {
            return -1;
        }
        return port;
    }
    
    /**
     * 标准化路径
     */
    private static String normalizePath(String path) {
        if (path == null || path.isEmpty()) {
            return "/";
        }
        
        // 确保路径以/开头
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        
        // 移除尾部的/（除非是根路径）
        if (path.length() > 1 && path.endsWith("/")) {
            path = path.substring(0, path.length() - 1);
        }
        
        // URL解码和重新编码（处理特殊字符）
        try {
            path = java.net.URLDecoder.decode(path, "UTF-8");
            path = java.net.URLEncoder.encode(path, "UTF-8")
                    .replace("%2F", "/")  // 保留路径分隔符
                    .replace("+", "%20"); // 空格编码为%20而不是+
        } catch (Exception e) {
            // 如果编码/解码失败，保持原样
        }
        
        return path;
    }
    
    /**
     * 标准化查询参数
     */
    private static String normalizeQuery(String query) {
        if (query == null || query.trim().isEmpty()) {
            return null;
        }
        
        Map<String, String> params = parseQueryParams(query);
        
        // 移除跟踪参数
        removeTrackingParams(params);
        
        // 如果没有参数了，返回null
        if (params.isEmpty()) {
            return null;
        }
        
        // 按参数名排序
        Map<String, String> sortedParams = new TreeMap<>(params);
        
        // 重新构建查询字符串
        StringBuilder queryBuilder = new StringBuilder();
        for (Map.Entry<String, String> entry : sortedParams.entrySet()) {
            if (queryBuilder.length() > 0) {
                queryBuilder.append("&");
            }
            queryBuilder.append(entry.getKey());
            if (entry.getValue() != null && !entry.getValue().isEmpty()) {
                queryBuilder.append("=").append(entry.getValue());
            }
        }
        
        return queryBuilder.toString();
    }
    
    /**
     * 解析查询参数
     */
    private static Map<String, String> parseQueryParams(String query) {
        Map<String, String> params = new LinkedHashMap<>();
        
        if (query == null || query.isEmpty()) {
            return params;
        }
        
        String[] pairs = query.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            String key = keyValue[0];
            String value = keyValue.length > 1 ? keyValue[1] : "";
            
            // URL解码
            try {
                key = java.net.URLDecoder.decode(key, "UTF-8");
                value = java.net.URLDecoder.decode(value, "UTF-8");
            } catch (Exception e) {
                // 解码失败时保持原样
            }
            
            params.put(key, value);
        }
        
        return params;
    }
    
    /**
     * 移除跟踪参数
     */
    private static void removeTrackingParams(Map<String, String> params) {
        for (String trackingParam : TRACKING_PARAMS) {
            params.remove(trackingParam);
        }
    }
    
    /**
     * 清理无效URL
     */
    private static String cleanInvalidUrl(String url) {
        if (url == null) {
            return null;
        }
        
        String cleaned = url.trim();
        
        // 移除常见的无效字符
        cleaned = cleaned.replaceAll("[\\s\\n\\r\\t]", "");
        
        // 限制长度
        if (cleaned.length() > 2000) {
            cleaned = cleaned.substring(0, 2000);
        }
        
        return cleaned;
    }
    
    /**
     * 提取域名
     */
    public static String extractDomain(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }
        
        try {
            String trimmedUrl = url.trim();
            if (!trimmedUrl.startsWith("http://") && !trimmedUrl.startsWith("https://")) {
                trimmedUrl = "https://" + trimmedUrl;
            }
            
            URL urlObj = new URL(trimmedUrl);
            return normalizeHost(urlObj.getHost());
            
        } catch (MalformedURLException e) {
            return null;
        }
    }
    
    /**
     * 提取路径（不包含查询参数）
     */
    public static String extractPath(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }
        
        try {
            String trimmedUrl = url.trim();
            if (!trimmedUrl.startsWith("http://") && !trimmedUrl.startsWith("https://")) {
                trimmedUrl = "https://" + trimmedUrl;
            }
            
            URL urlObj = new URL(trimmedUrl);
            return normalizePath(urlObj.getPath());
            
        } catch (MalformedURLException e) {
            return null;
        }
    }
    
    /**
     * 检查URL是否有效
     */
    public static boolean isValidUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return false;
        }
        
        try {
            String trimmedUrl = url.trim();
            if (!trimmedUrl.startsWith("http://") && !trimmedUrl.startsWith("https://")) {
                trimmedUrl = "https://" + trimmedUrl;
            }
            
            new URL(trimmedUrl);
            return true;
            
        } catch (MalformedURLException e) {
            return false;
        }
    }
    
    /**
     * 生成URL的简化版本（用于分组和统计）
     */
    public static String simplify(String url) {
        String normalized = normalize(url);
        if (normalized == null) {
            return null;
        }
        
        try {
            URL urlObj = new URL(normalized);
            
            // 只保留协议、域名和路径，移除查询参数
            StringBuilder simplified = new StringBuilder();
            simplified.append(urlObj.getProtocol()).append("://");
            simplified.append(urlObj.getHost());
            
            if (urlObj.getPort() != -1) {
                simplified.append(":").append(urlObj.getPort());
            }
            
            simplified.append(urlObj.getPath());
            
            return simplified.toString();
            
        } catch (MalformedURLException e) {
            return normalized;
        }
    }
}
