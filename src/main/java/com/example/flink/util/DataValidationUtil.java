package com.example.flink.util;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.regex.Pattern;

/**
 * 数据验证工具类
 * 提供各种数据格式验证和清洗的静态方法
 */
public class DataValidationUtil {
    
    // 邮箱格式验证
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    // 手机号格式验证（中国大陆）
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^1[3-9]\\d{9}$"
    );
    
    // URL格式验证
    private static final Pattern URL_PATTERN = Pattern.compile(
        "^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$"
    );
    
    // IP地址格式验证
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    /**
     * 验证字符串是否为空或null
     */
    public static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 验证字符串是否不为空
     */
    public static boolean isNotEmpty(String str) {
        return !isNullOrEmpty(str);
    }
    
    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        if (isNullOrEmpty(email)) {
            return false;
        }
        return EMAIL_PATTERN.matcher(email.trim()).matches();
    }
    
    /**
     * 验证手机号格式
     */
    public static boolean isValidPhone(String phone) {
        if (isNullOrEmpty(phone)) {
            return false;
        }
        return PHONE_PATTERN.matcher(phone.trim()).matches();
    }
    
    /**
     * 验证URL格式
     */
    public static boolean isValidUrl(String url) {
        if (isNullOrEmpty(url)) {
            return false;
        }
        return URL_PATTERN.matcher(url.trim()).matches();
    }
    
    /**
     * 验证IP地址格式
     */
    public static boolean isValidIpAddress(String ip) {
        if (isNullOrEmpty(ip)) {
            return false;
        }
        return IP_PATTERN.matcher(ip.trim()).matches();
    }
    
    /**
     * 验证数字范围
     */
    public static boolean isInRange(Long value, long min, long max) {
        return value != null && value >= min && value <= max;
    }
    
    /**
     * 验证时间戳是否在合理范围内
     */
    public static boolean isValidTimestamp(LocalDateTime timestamp) {
        if (timestamp == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneYearAgo = now.minusYears(1);
        LocalDateTime oneHourLater = now.plusHours(1);
        
        return timestamp.isAfter(oneYearAgo) && timestamp.isBefore(oneHourLater);
    }
    
    /**
     * 清理和标准化字符串
     */
    public static String cleanString(String str) {
        if (str == null) {
            return null;
        }
        
        // 去除首尾空格，转换为小写
        String cleaned = str.trim().toLowerCase();
        
        // 移除特殊字符（保留字母、数字、下划线、连字符）
        cleaned = cleaned.replaceAll("[^a-zA-Z0-9_-]", "");
        
        return cleaned.isEmpty() ? null : cleaned;
    }
    
    /**
     * 验证字符串长度
     */
    public static boolean isValidLength(String str, int minLength, int maxLength) {
        if (str == null) {
            return minLength == 0;
        }
        
        int length = str.trim().length();
        return length >= minLength && length <= maxLength;
    }
    
    /**
     * 验证数值是否为正数
     */
    public static boolean isPositive(Long value) {
        return value != null && value > 0;
    }
    
    /**
     * 验证数值是否为非负数
     */
    public static boolean isNonNegative(Long value) {
        return value != null && value >= 0;
    }
    
    /**
     * 标准化用户ID
     */
    public static String normalizeUserId(String userId) {
        if (isNullOrEmpty(userId)) {
            return null;
        }
        
        String normalized = userId.trim().toLowerCase();
        
        // 移除无效字符
        normalized = normalized.replaceAll("[^a-zA-Z0-9_-]", "");
        
        // 检查长度
        if (normalized.length() < 1 || normalized.length() > 50) {
            return null;
        }
        
        return normalized;
    }
    
    /**
     * 标准化事件类型
     */
    public static String normalizeEventType(String eventType) {
        if (isNullOrEmpty(eventType)) {
            return null;
        }
        
        String normalized = eventType.trim().toLowerCase();
        
        // 标准化常见的事件类型变体
        switch (normalized) {
            case "pageview":
            case "page-view":
            case "page_view":
                return "page_view";
            case "click":
            case "button_click":
            case "link_click":
                return "click";
            case "form_submit":
            case "form-submit":
            case "submit":
                return "form_submit";
            default:
                return normalized;
        }
    }
    
    /**
     * 验证会话ID格式
     */
    public static boolean isValidSessionId(String sessionId) {
        if (isNullOrEmpty(sessionId)) {
            return false;
        }
        
        String trimmed = sessionId.trim();
        
        // 会话ID应该是字母数字组合，长度在10-100之间
        return trimmed.matches("^[a-zA-Z0-9_-]{10,100}$");
    }
    
    /**
     * 清理和验证用户代理字符串
     */
    public static String cleanUserAgent(String userAgent) {
        if (isNullOrEmpty(userAgent)) {
            return null;
        }
        
        String cleaned = userAgent.trim();
        
        // 限制长度，防止过长的用户代理字符串
        if (cleaned.length() > 500) {
            cleaned = cleaned.substring(0, 500);
        }
        
        return cleaned;
    }
    
    /**
     * 验证持续时间是否合理
     */
    public static boolean isValidDuration(Long duration) {
        if (duration == null) {
            return true; // 可选字段
        }
        
        // 持续时间应该在0到24小时之间
        return duration >= 0 && duration <= 86400;
    }
}
