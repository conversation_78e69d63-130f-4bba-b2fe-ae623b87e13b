package com.example.flink.function;

import com.example.flink.model.CleanedUserEvent;
import com.example.flink.model.UserEvent;
import com.example.flink.util.DataValidationUtil;
import com.example.flink.util.IpMaskingUtil;
import com.example.flink.util.UrlNormalizationUtil;
import com.example.flink.util.UserAgentParserUtil;
import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.regex.Pattern;

/**
 * 数据清洗函数
 * 负责将原始的UserEvent转换为清洗后的CleanedUserEvent
 */
public class DataCleaningFunction extends RichMapFunction<UserEvent, CleanedUserEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(DataCleaningFunction.class);
    
    // 机器人检测的正则表达式
    private static final Pattern BOT_PATTERN = Pattern.compile(
        "(?i).*(bot|crawler|spider|scraper|wget|curl).*"
    );
    
    // 移动设备检测的正则表达式
    private static final Pattern MOBILE_PATTERN = Pattern.compile(
        "(?i).*(mobile|android|iphone|ipad|blackberry|windows phone).*"
    );
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        logger.info("DataCleaningFunction initialized");
    }
    
    @Override
    public CleanedUserEvent map(UserEvent userEvent) throws Exception {
        try {
            // 创建清洗后的事件对象
            CleanedUserEvent cleanedEvent = new CleanedUserEvent(userEvent);
            
            // 1. 数据验证和基础清洗
            performBasicCleaning(cleanedEvent);
            
            // 2. URL标准化
            performUrlNormalization(cleanedEvent);
            
            // 3. IP地址脱敏
            performIpMasking(cleanedEvent);
            
            // 4. User Agent解析
            performUserAgentParsing(cleanedEvent, userEvent.getUserAgent());
            
            // 5. 设备类型标准化
            performDeviceTypeNormalization(cleanedEvent);
            
            // 6. 机器人检测
            performBotDetection(cleanedEvent, userEvent.getUserAgent());
            
            // 7. 地理位置解析
            performLocationParsing(cleanedEvent);
            
            // 8. 数据异常值处理
            performAnomalyHandling(cleanedEvent);
            
            logger.debug("Successfully cleaned event for user: {}", cleanedEvent.getUserId());
            return cleanedEvent;
            
        } catch (Exception e) {
            logger.error("Error cleaning event for user: {}", userEvent.getUserId(), e);
            throw e;
        }
    }
    
    /**
     * 执行基础数据清洗
     */
    private void performBasicCleaning(CleanedUserEvent event) {
        // 清理用户ID
        if (event.getUserId() != null) {
            event.setUserId(event.getUserId().trim().toLowerCase());
        }
        
        // 标准化事件类型
        if (event.getEventType() != null) {
            event.setEventType(event.getEventType().trim().toLowerCase());
        }
        
        // 清理会话ID
        if (event.getSessionId() != null) {
            event.setSessionId(event.getSessionId().trim());
        }
        
        // 验证时间戳
        if (event.getTimestamp() == null) {
            event.setTimestamp(LocalDateTime.now());
        }
    }
    
    /**
     * 执行URL标准化
     */
    private void performUrlNormalization(CleanedUserEvent event) {
        if (event.getPageUrl() != null) {
            String normalizedUrl = UrlNormalizationUtil.normalize(event.getPageUrl());
            event.setNormalizedUrl(normalizedUrl);
        }
    }
    
    /**
     * 执行IP地址脱敏
     */
    private void performIpMasking(CleanedUserEvent event) {
        if (event.getIpAddress() != null) {
            String maskedIp = IpMaskingUtil.maskIpAddress(event.getIpAddress());
            event.setMaskedIp(maskedIp);
        }
    }
    
    /**
     * 执行User Agent解析
     */
    private void performUserAgentParsing(CleanedUserEvent event, String userAgent) {
        if (userAgent != null && !userAgent.trim().isEmpty()) {
            UserAgentParserUtil.ParseResult parseResult = UserAgentParserUtil.parse(userAgent);
            event.setBrowser(parseResult.getBrowser());
            event.setBrowserVersion(parseResult.getBrowserVersion());
            event.setIsMobile(parseResult.isMobile());
        }
    }
    
    /**
     * 执行设备类型标准化
     */
    private void performDeviceTypeNormalization(CleanedUserEvent event) {
        if (event.getDeviceType() != null) {
            String deviceType = event.getDeviceType().trim().toLowerCase();
            switch (deviceType) {
                case "mobile":
                case "smartphone":
                case "phone":
                    event.setDeviceType("mobile");
                    break;
                case "tablet":
                case "ipad":
                    event.setDeviceType("tablet");
                    break;
                case "desktop":
                case "pc":
                case "computer":
                    event.setDeviceType("desktop");
                    break;
                default:
                    event.setDeviceType("unknown");
            }
        }
    }
    
    /**
     * 执行机器人检测
     */
    private void performBotDetection(CleanedUserEvent event, String userAgent) {
        boolean isBot = false;
        
        if (userAgent != null) {
            isBot = BOT_PATTERN.matcher(userAgent).matches();
        }
        
        // 额外的机器人检测逻辑
        if (!isBot && event.getDuration() != null) {
            // 如果页面停留时间异常短（小于1秒）或异常长（大于1小时），可能是机器人
            long duration = event.getDuration();
            if (duration < 1 || duration > 3600) {
                isBot = true;
            }
        }
        
        event.setIsBot(isBot);
    }
    
    /**
     * 执行地理位置解析
     */
    private void performLocationParsing(CleanedUserEvent event) {
        if (event.getLocation() != null && !event.getLocation().trim().isEmpty()) {
            String location = event.getLocation().trim();
            String[] parts = location.split(",");
            
            if (parts.length >= 2) {
                event.setCountry(parts[0].trim());
                event.setCity(parts[1].trim());
            } else {
                event.setCountry(location);
            }
        }
    }
    
    /**
     * 执行异常值处理
     */
    private void performAnomalyHandling(CleanedUserEvent event) {
        // 处理异常的持续时间
        if (event.getDuration() != null) {
            long duration = event.getDuration();
            if (duration < 0) {
                event.setDuration(0L);
            } else if (duration > 86400) { // 大于24小时
                event.setDuration(86400L);
            }
        }
        
        // 验证时间戳是否在合理范围内
        if (event.getTimestamp() != null) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime oneYearAgo = now.minusYears(1);
            LocalDateTime oneHourLater = now.plusHours(1);
            
            if (event.getTimestamp().isBefore(oneYearAgo) || 
                event.getTimestamp().isAfter(oneHourLater)) {
                logger.warn("Suspicious timestamp detected for user {}: {}", 
                           event.getUserId(), event.getTimestamp());
                event.setTimestamp(now);
            }
        }
    }
}
