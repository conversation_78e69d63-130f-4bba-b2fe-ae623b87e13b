package com.example.flink.function;

import com.example.flink.model.UserEvent;
import org.apache.flink.api.common.functions.FilterFunction;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.regex.Pattern;

/**
 * 数据验证过滤函数
 * 用于过滤掉无效或不符合要求的数据记录
 */
public class DataValidationFunction implements FilterFunction<UserEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(DataValidationFunction.class);
    
    // 用户ID格式验证正则表达式
    private static final Pattern USER_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{1,50}$");
    
    // 会话ID格式验证正则表达式
    private static final Pattern SESSION_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]{1,100}$");
    
    // IP地址格式验证正则表达式
    private static final Pattern IP_PATTERN = Pattern.compile(
        "^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$"
    );
    
    // URL格式验证正则表达式
    private static final Pattern URL_PATTERN = Pattern.compile(
        "^https?://[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}(/.*)?$"
    );
    
    // 有效的事件类型
    private static final String[] VALID_EVENT_TYPES = {
        "page_view", "click", "scroll", "form_submit", "download", 
        "search", "login", "logout", "purchase", "add_to_cart"
    };
    
    @Override
    public boolean filter(UserEvent event) throws Exception {
        try {
            // 1. 基础字段验证
            if (!validateBasicFields(event)) {
                logger.debug("Event failed basic field validation: {}", event.getUserId());
                return false;
            }
            
            // 2. 用户ID验证
            if (!validateUserId(event.getUserId())) {
                logger.debug("Event failed user ID validation: {}", event.getUserId());
                return false;
            }
            
            // 3. 事件类型验证
            if (!validateEventType(event.getEventType())) {
                logger.debug("Event failed event type validation: {}", event.getEventType());
                return false;
            }
            
            // 4. 时间戳验证
            if (!validateTimestamp(event.getTimestamp())) {
                logger.debug("Event failed timestamp validation: {}", event.getTimestamp());
                return false;
            }
            
            // 5. 会话ID验证
            if (!validateSessionId(event.getSessionId())) {
                logger.debug("Event failed session ID validation: {}", event.getSessionId());
                return false;
            }
            
            // 6. IP地址验证（可选字段）
            if (event.getIpAddress() != null && !validateIpAddress(event.getIpAddress())) {
                logger.debug("Event failed IP address validation: {}", event.getIpAddress());
                return false;
            }
            
            // 7. URL验证（可选字段）
            if (event.getPageUrl() != null && !validateUrl(event.getPageUrl())) {
                logger.debug("Event failed URL validation: {}", event.getPageUrl());
                return false;
            }
            
            // 8. 持续时间验证（可选字段）
            if (event.getDuration() != null && !validateDuration(event.getDuration())) {
                logger.debug("Event failed duration validation: {}", event.getDuration());
                return false;
            }
            
            logger.debug("Event passed all validations: {}", event.getUserId());
            return true;
            
        } catch (Exception e) {
            logger.error("Error validating event: {}", event, e);
            return false;
        }
    }
    
    /**
     * 验证基础必填字段
     */
    private boolean validateBasicFields(UserEvent event) {
        return event != null &&
               event.getUserId() != null && !event.getUserId().trim().isEmpty() &&
               event.getEventType() != null && !event.getEventType().trim().isEmpty() &&
               event.getTimestamp() != null &&
               event.getSessionId() != null && !event.getSessionId().trim().isEmpty();
    }
    
    /**
     * 验证用户ID格式
     */
    private boolean validateUserId(String userId) {
        if (userId == null || userId.trim().isEmpty()) {
            return false;
        }
        
        String trimmedUserId = userId.trim();
        return USER_ID_PATTERN.matcher(trimmedUserId).matches();
    }
    
    /**
     * 验证事件类型
     */
    private boolean validateEventType(String eventType) {
        if (eventType == null || eventType.trim().isEmpty()) {
            return false;
        }
        
        String trimmedEventType = eventType.trim().toLowerCase();
        for (String validType : VALID_EVENT_TYPES) {
            if (validType.equals(trimmedEventType)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 验证时间戳
     */
    private boolean validateTimestamp(LocalDateTime timestamp) {
        if (timestamp == null) {
            return false;
        }
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime oneYearAgo = now.minusYears(1);
        LocalDateTime oneHourLater = now.plusHours(1);
        
        // 时间戳应该在过去一年到未来一小时之间
        return timestamp.isAfter(oneYearAgo) && timestamp.isBefore(oneHourLater);
    }
    
    /**
     * 验证会话ID格式
     */
    private boolean validateSessionId(String sessionId) {
        if (sessionId == null || sessionId.trim().isEmpty()) {
            return false;
        }
        
        String trimmedSessionId = sessionId.trim();
        return SESSION_ID_PATTERN.matcher(trimmedSessionId).matches();
    }
    
    /**
     * 验证IP地址格式
     */
    private boolean validateIpAddress(String ipAddress) {
        if (ipAddress == null || ipAddress.trim().isEmpty()) {
            return true; // IP地址是可选字段
        }
        
        String trimmedIp = ipAddress.trim();
        return IP_PATTERN.matcher(trimmedIp).matches();
    }
    
    /**
     * 验证URL格式
     */
    private boolean validateUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return true; // URL是可选字段
        }
        
        String trimmedUrl = url.trim();
        return URL_PATTERN.matcher(trimmedUrl).matches();
    }
    
    /**
     * 验证持续时间
     */
    private boolean validateDuration(Long duration) {
        if (duration == null) {
            return true; // 持续时间是可选字段
        }
        
        // 持续时间应该在0到24小时之间（86400秒）
        return duration >= 0 && duration <= 86400;
    }
}
