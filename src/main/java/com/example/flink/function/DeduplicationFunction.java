package com.example.flink.function;

import com.example.flink.model.CleanedUserEvent;
import org.apache.flink.api.common.state.ValueState;
import org.apache.flink.api.common.state.ValueStateDescriptor;
import org.apache.flink.api.common.typeinfo.TypeHint;
import org.apache.flink.api.common.typeinfo.TypeInformation;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.streaming.api.functions.KeyedProcessFunction;
import org.apache.flink.util.Collector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 数据去重函数
 * 基于用户ID、事件类型、时间窗口进行去重处理
 * 使用Flink的状态管理来跟踪已处理的事件
 */
public class DeduplicationFunction extends KeyedProcessFunction<String, CleanedUserEvent, CleanedUserEvent> {
    
    private static final Logger logger = LoggerFactory.getLogger(DeduplicationFunction.class);
    
    // 去重时间窗口（秒）
    private static final long DEDUPLICATION_WINDOW_SECONDS = 300; // 5分钟
    
    // 状态：存储最近处理的事件签名
    private transient ValueState<Set<String>> recentEventSignatures;
    
    // 状态：存储最后清理时间
    private transient ValueState<Long> lastCleanupTime;
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化状态
        ValueStateDescriptor<Set<String>> signaturesDescriptor = new ValueStateDescriptor<>(
            "recent-event-signatures",
            TypeInformation.of(new TypeHint<Set<String>>() {})
        );
        recentEventSignatures = getRuntimeContext().getState(signaturesDescriptor);
        
        ValueStateDescriptor<Long> cleanupTimeDescriptor = new ValueStateDescriptor<>(
            "last-cleanup-time",
            Long.class
        );
        lastCleanupTime = getRuntimeContext().getState(cleanupTimeDescriptor);
        
        logger.info("DeduplicationFunction initialized with window: {} seconds", 
                   DEDUPLICATION_WINDOW_SECONDS);
    }
    
    @Override
    public void processElement(CleanedUserEvent event, Context context, Collector<CleanedUserEvent> out) 
            throws Exception {
        
        try {
            // 生成事件签名
            String eventSignature = generateEventSignature(event);
            
            // 获取当前时间戳
            long currentTime = System.currentTimeMillis() / 1000; // 转换为秒
            
            // 执行定期清理
            performPeriodicCleanup(currentTime);
            
            // 获取最近的事件签名集合
            Set<String> signatures = recentEventSignatures.value();
            if (signatures == null) {
                signatures = new HashSet<>();
            }
            
            // 检查是否为重复事件
            if (signatures.contains(eventSignature)) {
                logger.debug("Duplicate event detected and filtered: {}", eventSignature);
                return; // 过滤掉重复事件
            }
            
            // 添加新的事件签名
            signatures.add(eventSignature);
            recentEventSignatures.update(signatures);
            
            // 设置定时器进行清理
            long cleanupTime = currentTime + DEDUPLICATION_WINDOW_SECONDS;
            context.timerService().registerProcessingTimeTimer(cleanupTime * 1000); // 转换回毫秒
            
            // 输出非重复事件
            out.collect(event);
            logger.debug("Event processed and forwarded: {}", eventSignature);
            
        } catch (Exception e) {
            logger.error("Error processing event for deduplication: {}", event, e);
            // 在出错时仍然输出事件，避免数据丢失
            out.collect(event);
        }
    }
    
    @Override
    public void onTimer(long timestamp, OnTimerContext ctx, Collector<CleanedUserEvent> out) 
            throws Exception {
        
        // 定时器触发时执行清理
        long currentTime = System.currentTimeMillis() / 1000;
        performPeriodicCleanup(currentTime);
    }
    
    /**
     * 生成事件签名用于去重
     * 基于用户ID、事件类型、时间戳（精确到分钟）、页面URL等关键字段
     */
    private String generateEventSignature(CleanedUserEvent event) {
        StringBuilder signature = new StringBuilder();
        
        // 用户ID
        signature.append(event.getUserId() != null ? event.getUserId() : "null");
        signature.append("|");
        
        // 事件类型
        signature.append(event.getEventType() != null ? event.getEventType() : "null");
        signature.append("|");
        
        // 时间戳（精确到分钟，减少时间精度以增加去重效果）
        if (event.getTimestamp() != null) {
            LocalDateTime timestamp = event.getTimestamp();
            signature.append(timestamp.getYear()).append("-")
                     .append(timestamp.getMonthValue()).append("-")
                     .append(timestamp.getDayOfMonth()).append(" ")
                     .append(timestamp.getHour()).append(":")
                     .append(timestamp.getMinute());
        } else {
            signature.append("null");
        }
        signature.append("|");
        
        // 标准化URL（如果存在）
        signature.append(event.getNormalizedUrl() != null ? event.getNormalizedUrl() : "null");
        signature.append("|");
        
        // 会话ID
        signature.append(event.getSessionId() != null ? event.getSessionId() : "null");
        
        return signature.toString();
    }
    
    /**
     * 执行定期清理，移除过期的事件签名
     */
    private void performPeriodicCleanup(long currentTime) throws Exception {
        Long lastCleanup = lastCleanupTime.value();
        
        // 如果距离上次清理超过窗口时间的一半，则执行清理
        if (lastCleanup == null || (currentTime - lastCleanup) > (DEDUPLICATION_WINDOW_SECONDS / 2)) {
            
            Set<String> signatures = recentEventSignatures.value();
            if (signatures != null && !signatures.isEmpty()) {
                // 简单的清理策略：清空所有签名
                // 在实际生产环境中，可以实现更精细的基于时间戳的清理
                signatures.clear();
                recentEventSignatures.update(signatures);
                
                logger.debug("Performed cleanup of event signatures");
            }
            
            lastCleanupTime.update(currentTime);
        }
    }
    
    /**
     * 生成简化的事件签名（用于快速去重）
     * 仅基于核心字段，适用于对去重精度要求不高的场景
     */
    private String generateSimpleEventSignature(CleanedUserEvent event) {
        return String.format("%s|%s|%s", 
                           event.getUserId() != null ? event.getUserId() : "null",
                           event.getEventType() != null ? event.getEventType() : "null",
                           event.getSessionId() != null ? event.getSessionId() : "null");
    }
    
    /**
     * 检查事件是否为可能的重复事件
     * 基于业务逻辑的额外重复检测
     */
    private boolean isPotentialDuplicate(CleanedUserEvent event) {
        // 检查是否为机器人流量（机器人流量更容易产生重复）
        if (Boolean.TRUE.equals(event.getIsBot())) {
            return true;
        }
        
        // 检查持续时间是否异常（可能表示重复提交）
        if (event.getDuration() != null && event.getDuration() == 0) {
            return true;
        }
        
        return false;
    }
}
