package com.example.flink.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 清洗后的用户事件数据模型
 * 表示经过数据清洗处理后的标准化用户行为数据
 */
public class CleanedUserEvent {
    
    @JsonProperty("user_id")
    private String userId;
    
    @JsonProperty("event_type")
    private String eventType;
    
    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    @JsonProperty("page_url")
    private String pageUrl;
    
    @JsonProperty("normalized_url")
    private String normalizedUrl; // 标准化后的URL
    
    @JsonProperty("ip_address")
    private String ipAddress;
    
    @JsonProperty("masked_ip")
    private String maskedIp; // 脱敏后的IP地址
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("device_type")
    private String deviceType;
    
    @JsonProperty("browser")
    private String browser;
    
    @JsonProperty("browser_version")
    private String browserVersion;
    
    @JsonProperty("location")
    private String location;
    
    @JsonProperty("country")
    private String country;
    
    @JsonProperty("city")
    private String city;
    
    @JsonProperty("duration")
    private Long duration;
    
    @JsonProperty("is_bot")
    private Boolean isBot; // 是否为机器人流量
    
    @JsonProperty("is_mobile")
    private Boolean isMobile; // 是否为移动设备
    
    @JsonProperty("processing_timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processingTimestamp; // 数据处理时间
    
    // 默认构造函数
    public CleanedUserEvent() {
        this.processingTimestamp = LocalDateTime.now();
    }
    
    // 从原始UserEvent构造
    public CleanedUserEvent(UserEvent userEvent) {
        this.userId = userEvent.getUserId();
        this.eventType = userEvent.getEventType();
        this.timestamp = userEvent.getTimestamp();
        this.pageUrl = userEvent.getPageUrl();
        this.ipAddress = userEvent.getIpAddress();
        this.sessionId = userEvent.getSessionId();
        this.deviceType = userEvent.getDeviceType();
        this.browser = userEvent.getBrowser();
        this.location = userEvent.getLocation();
        this.duration = userEvent.getDuration();
        this.processingTimestamp = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getEventType() { return eventType; }
    public void setEventType(String eventType) { this.eventType = eventType; }
    
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    
    public String getPageUrl() { return pageUrl; }
    public void setPageUrl(String pageUrl) { this.pageUrl = pageUrl; }
    
    public String getNormalizedUrl() { return normalizedUrl; }
    public void setNormalizedUrl(String normalizedUrl) { this.normalizedUrl = normalizedUrl; }
    
    public String getIpAddress() { return ipAddress; }
    public void setIpAddress(String ipAddress) { this.ipAddress = ipAddress; }
    
    public String getMaskedIp() { return maskedIp; }
    public void setMaskedIp(String maskedIp) { this.maskedIp = maskedIp; }
    
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    
    public String getDeviceType() { return deviceType; }
    public void setDeviceType(String deviceType) { this.deviceType = deviceType; }
    
    public String getBrowser() { return browser; }
    public void setBrowser(String browser) { this.browser = browser; }
    
    public String getBrowserVersion() { return browserVersion; }
    public void setBrowserVersion(String browserVersion) { this.browserVersion = browserVersion; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }
    
    public String getCity() { return city; }
    public void setCity(String city) { this.city = city; }
    
    public Long getDuration() { return duration; }
    public void setDuration(Long duration) { this.duration = duration; }
    
    public Boolean getIsBot() { return isBot; }
    public void setIsBot(Boolean isBot) { this.isBot = isBot; }
    
    public Boolean getIsMobile() { return isMobile; }
    public void setIsMobile(Boolean isMobile) { this.isMobile = isMobile; }
    
    public LocalDateTime getProcessingTimestamp() { return processingTimestamp; }
    public void setProcessingTimestamp(LocalDateTime processingTimestamp) { 
        this.processingTimestamp = processingTimestamp; 
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CleanedUserEvent that = (CleanedUserEvent) o;
        return Objects.equals(userId, that.userId) &&
               Objects.equals(eventType, that.eventType) &&
               Objects.equals(timestamp, that.timestamp) &&
               Objects.equals(sessionId, that.sessionId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(userId, eventType, timestamp, sessionId);
    }
    
    @Override
    public String toString() {
        return "CleanedUserEvent{" +
                "userId='" + userId + '\'' +
                ", eventType='" + eventType + '\'' +
                ", timestamp=" + timestamp +
                ", normalizedUrl='" + normalizedUrl + '\'' +
                ", maskedIp='" + maskedIp + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", deviceType='" + deviceType + '\'' +
                ", browser='" + browser + '\'' +
                ", isBot=" + isBot +
                ", isMobile=" + isMobile +
                ", processingTimestamp=" + processingTimestamp +
                '}';
    }
}
