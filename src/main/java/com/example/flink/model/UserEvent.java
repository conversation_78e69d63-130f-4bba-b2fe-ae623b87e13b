package com.example.flink.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户事件数据模型
 * 表示需要清洗的原始用户行为数据
 */
public class UserEvent {
    
    @JsonProperty("user_id")
    private String userId;
    
    @JsonProperty("event_type")
    private String eventType;
    
    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timestamp;
    
    @JsonProperty("page_url")
    private String pageUrl;
    
    @JsonProperty("user_agent")
    private String userAgent;
    
    @JsonProperty("ip_address")
    private String ipAddress;
    
    @JsonProperty("session_id")
    private String sessionId;
    
    @JsonProperty("device_type")
    private String deviceType;
    
    @JsonProperty("browser")
    private String browser;
    
    @JsonProperty("location")
    private String location;
    
    @JsonProperty("duration")
    private Long duration; // 页面停留时间，单位：秒
    
    // 默认构造函数
    public UserEvent() {}
    
    // 全参构造函数
    public UserEvent(String userId, String eventType, LocalDateTime timestamp, 
                    String pageUrl, String userAgent, String ipAddress, 
                    String sessionId, String deviceType, String browser, 
                    String location, Long duration) {
        this.userId = userId;
        this.eventType = eventType;
        this.timestamp = timestamp;
        this.pageUrl = pageUrl;
        this.userAgent = userAgent;
        this.ipAddress = ipAddress;
        this.sessionId = sessionId;
        this.deviceType = deviceType;
        this.browser = browser;
        this.location = location;
        this.duration = duration;
    }
    
    // Getter和Setter方法
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }
    
    public String getEventType() { return eventType; }
    public void setEventType(String eventType) { this.eventType = eventType; }
    
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    
    public String getPageUrl() { return pageUrl; }
    public void setPageUrl(String pageUrl) { this.pageUrl = pageUrl; }
    
    public String getUserAgent() { return userAgent; }
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
    
    public String getIpAddress() { return ipAddress; }
    public void setIpAddress(String ipAddress) { this.ipAddress = ipAddress; }
    
    public String getSessionId() { return sessionId; }
    public void setSessionId(String sessionId) { this.sessionId = sessionId; }
    
    public String getDeviceType() { return deviceType; }
    public void setDeviceType(String deviceType) { this.deviceType = deviceType; }
    
    public String getBrowser() { return browser; }
    public void setBrowser(String browser) { this.browser = browser; }
    
    public String getLocation() { return location; }
    public void setLocation(String location) { this.location = location; }
    
    public Long getDuration() { return duration; }
    public void setDuration(Long duration) { this.duration = duration; }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserEvent userEvent = (UserEvent) o;
        return Objects.equals(userId, userEvent.userId) &&
               Objects.equals(eventType, userEvent.eventType) &&
               Objects.equals(timestamp, userEvent.timestamp) &&
               Objects.equals(sessionId, userEvent.sessionId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(userId, eventType, timestamp, sessionId);
    }
    
    @Override
    public String toString() {
        return "UserEvent{" +
                "userId='" + userId + '\'' +
                ", eventType='" + eventType + '\'' +
                ", timestamp=" + timestamp +
                ", pageUrl='" + pageUrl + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", sessionId='" + sessionId + '\'' +
                ", deviceType='" + deviceType + '\'' +
                ", browser='" + browser + '\'' +
                ", location='" + location + '\'' +
                ", duration=" + duration +
                '}';
    }
    
    /**
     * 检查事件数据是否有效
     * @return true if valid, false otherwise
     */
    public boolean isValid() {
        return userId != null && !userId.trim().isEmpty() &&
               eventType != null && !eventType.trim().isEmpty() &&
               timestamp != null &&
               sessionId != null && !sessionId.trim().isEmpty();
    }
}
