# Flink Data Cleaning Application Configuration

# Kafka Configuration
kafka.bootstrap.servers=localhost:9092
kafka.input.topic=user-events
kafka.output.topic=cleaned-user-events
kafka.group.id=data-cleaning-group
kafka.auto.offset.reset=latest

# File Configuration
input.file.path=input/user_events.json
output.file.path=output/cleaned_events

# Data Cleaning Configuration
cleaning.deduplication.window.seconds=300
cleaning.validation.strict.mode=false
cleaning.ip.masking.level=MEDIUM
cleaning.url.remove.tracking.params=true

# Performance Configuration
flink.parallelism=4
flink.checkpoint.interval=60000
flink.checkpoint.timeout=300000
flink.restart.strategy=fixed-delay
flink.restart.attempts=3
flink.restart.delay=10000

# Logging Configuration
logging.level.root=INFO
logging.level.com.example.flink=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=logs/flink-data-cleaning.log
logging.file.max-size=100MB
logging.file.max-history=30

# Metrics Configuration
metrics.enabled=true
metrics.reporters=slf4j
metrics.reporter.slf4j.class=org.apache.flink.metrics.slf4j.Slf4jReporter
metrics.reporter.slf4j.interval=60

# Memory Configuration
taskmanager.memory.process.size=2048m
taskmanager.memory.flink.size=1536m
jobmanager.memory.process.size=1024m

# Network Configuration
taskmanager.network.memory.fraction=0.1
taskmanager.network.memory.min=64mb
taskmanager.network.memory.max=1gb

# State Backend Configuration
state.backend=filesystem
state.checkpoints.dir=file:///tmp/flink-checkpoints
state.savepoints.dir=file:///tmp/flink-savepoints
state.backend.incremental=true

# Security Configuration (if needed)
# security.ssl.enabled=false
# security.ssl.keystore=
# security.ssl.keystore-password=
# security.ssl.truststore=
# security.ssl.truststore-password=
